<template>
  <div class="dr-relate-card-view">
    <div class="dr-relate-card-header">
      <!-- <h4 class="title">关联引用</h4> -->
      <el-button>引用数据</el-button>
    </div>
    <el-row :gutter="10" style="width: 100%">
      <!-- <slot></slot> -->
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { withDefaults, defineProps, defineOptions, defineSlots, watch } from 'vue'

defineOptions({
  inheritAttrs: false
})

const slots = defineSlots<{
  default?(props: any): any
}>()
console.log("🚀 linwu ~ index.vue:23 ~ defineSlots:", defineSlots, watch)

watch(() => slots, () => {
  console.log('slots', slots)
}, {
  deep: true,
  immediate: true
})

type PropsType = {
  relatedValue: {
    formId: string
    rules: any[]
  }
  colSpan?: number
  relateButtonText?: string
  formCreateInject: any
}

const props = withDefaults(defineProps<PropsType>(), {
  relateButtonText: '引用数据',
  colSpan: 24
})

const loadRule = () => {
  const rules =
    props.relatedValue.rules.map((rule) => {
      return rule ? {
        type: 'col',
        props: {
          span: rule.type === 'DrTableForm' ? 24 : props.colSpan
        },
        display: true,
        hidden: false,
        _fc_drag_tag: 'col',
        children: [
          {
            ...rule,
            preview: true
          }
        ]
      } : {}
    }) || []

  // eslint-disable-next-line vue/no-mutating-props
  props.formCreateInject.rule.children = [
    {
      type: 'template',
      _fc_drag_skip: true,
      children: rules
    }
  ]
}

if (props.relatedValue?.rules) {
  loadRule()
}

watch(
  () => [props.relatedValue, props.colSpan],
  () => {
    loadRule()
  },
  {
    deep: true
  }
)
</script>

<style lang="scss" scoped>
.dr-relate-card-view {
  width: 100%;

  .dr-relate-card-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .title {
      margin: 0;
    }
  }

  ::v-deep(.dr-table-form) {
    width: 100%;
    overflow-x: auto;

    .dr-table-row {
      display: table;
      width: 100%;
      overflow-x: auto;

      .fc-form-col {
        display: table-cell;
        width: 184px;
        height: 200px;
      }
    }
  }
}
</style>
